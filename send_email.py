import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta

from email.utils import formataddr
import base64

# 邮件配置
MAIL_HOST = "*************"
MAIL_PORT = 25
SENDER = "<EMAIL>"
SENDER_NAME = "厦门G8.6基地新品新技术导入部CF新品导入科"
# 测试账号
# RECEIVERS = ['<EMAIL>']
# RECEIVERS_NAMES = ['LaiWeiXuan赖伟煊']
RECEIVERS = ['<EMAIL>', ]
RECEIVERS_NAMES = ['厦门G8.6基地新品新技术导入部CF新品导入科', ]


def Header(string):
    """编码中文字符串用于邮件头部"""
    b64 = base64.b64encode(string.encode('utf-8'))
    hdr = '=?utf-8?b?{}?='.format(b64)
    return hdr


def send_email(html_content):
    """发送电子邮件"""
    try:
        # 建立SMTP连接
        smtp_obj = smtplib.SMTP(MAIL_HOST, MAIL_PORT)
        smtp_obj.starttls()
        smtp_obj.ehlo_or_helo_if_needed()

        # 创建邮件消息
        message = MIMEMultipart()
        message.attach(MIMEText(html_content, "html", "utf-8"))

        # 设置邮件头部信息
        message["From"] = formataddr((Header(SENDER_NAME), SENDER))

        # 构建收件人列表
        recipients = []
        for i, receiver in enumerate(RECEIVERS):
            if i < len(RECEIVERS_NAMES):
                recipients.append(formataddr((Header(RECEIVERS_NAMES[i]), receiver)))
            else:
                recipients.append(receiver)

        message["To"] = ", ".join(recipients)

        date_str = datetime.now().strftime("%Y-%m-%d")
        full_subject = "{} - {}".format(Header("卡夹处理提醒"), date_str)
        message["Subject"] = full_subject

        # 发送邮件
        smtp_obj.sendmail(SENDER, RECEIVERS, message.as_string())
        print("Email sent successfully")
        return True

    except Exception as e:
        print("Send email failed: {}".format(e))
        return False

    finally:
        try:
            smtp_obj.quit()
        except:
            pass


def main(html_content):
    """主函数"""
    try:
        print("Starting email sending...")

        if not html_content:
            print("No HTML content provided, email will not be sent")
            return

        # 发送邮件
        if send_email(html_content):
            print("Email sent successfully")
        else:
            print("Failed to send email")

    except Exception as e:
        print("Execution failed: {}".format(e))


html_content = Document.Properties["html_content"]
main(html_content)
