# CPython 3.11

import pandas as pd
from datetime import datetime

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'
    wip_list = pd.read_excel('WIP.xlsx')

# 预定义字段
grade_priority = ['P', 'S', 'T', 'I', 'G', 'R',]
hold_state_priority = ['OnHold', 'NotOnHold',]

def get_highest_priority(values, priority_list):
    """根据优先级列表获取最高优先级的值"""
    for priority in priority_list:
        if priority in values:
            return priority
    return None

def generate_html_content(carrier_df):
    """生成HTML邮件内容"""
    # 过滤条件：(Lot Grade列为R) 或 (Lot Grade为P 且 Qty≤3)
    filtered_df = carrier_df[
        (carrier_df['Lot Grade'] == 'R') |
        ((carrier_df['Lot Grade'] == 'P') & (carrier_df['Qty'] <= 3))
    ]

    if len(filtered_df) == 0:
        return None

    # 获取唯一值用于颜色映射
    unique_product_ids = sorted(filtered_df['Product ID'].unique())
    unique_lot_grades = sorted(filtered_df['Lot Grade'].unique())
    unique_hold_states = sorted(filtered_df['Hold State'].unique())

    # 创建颜色映射
    product_id_colors = ['#FFE6E6', '#E6F3FF', '#E6FFE6', '#FFF0E6', '#F0E6FF', '#E6FFFF']
    product_id_color_map = {pid: product_id_colors[i % len(product_id_colors)]
                            for i, pid in enumerate(unique_product_ids)}

    lot_grade_colors = ['#FFB6C1', '#98FB98', '#87CEFA', '#DDA0DD', '#F0E68C', '#E6E6FA']
    lot_grade_color_map = {lg: lot_grade_colors[i % len(lot_grade_colors)]
                           for i, lg in enumerate(unique_lot_grades)}

    hold_state_colors = ['#FFA07A', '#90EE90', '#ADD8E6', '#D8BFD8', '#FAFAD2', '#B0C4DE']
    hold_state_color_map = {hs: hold_state_colors[i % len(hold_state_colors)]
                            for i, hs in enumerate(unique_hold_states)}

    # 计算Stay Time颜色渐变范围
    min_stay_time = filtered_df['Stay Time(H)'].min()
    max_stay_time = filtered_df['Stay Time(H)'].max()
    stay_time_range = max_stay_time - min_stay_time

    # 创建HTML内容
    html_content = """
    <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; }
                table {
                    border-collapse: collapse;
                    width: 100%;
                    margin-top: 20px;
                }
                th {
                    background-color: #4A90E2;
                    color: white;
                    padding: 6px;
                    text-align: center;
                    border: 1px solid #ddd;
                }
                td {
                    padding: 6px;
                    border: 1px solid #ddd;
                    text-align: center;
                    max-width: 120px;
                    word-wrap: break-word;
                }
                .comment-cell {
                    max-width: 150px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    cursor: help;
                }
                .note {
                    color: #666;
                    font-size: 13px;
                    margin: 5px 0;
                    line-height: 1.4;
                }
                .notes-container {
                    background-color: #f9f9f9;
                    padding: 10px;
                    border-left: 3px solid #4A90E2;
                    margin: 10px 0;
                }
            </style>
        </head>
        <body>
            <h3>P等/R等卡夹信息如下，请妥善处理：</h3>
            <table>
                <thead>
                    <tr>
                        <th>Product ID</th>
                        <th>CST ID</th>
                        <th>Lot Grade</th>
                        <th>Qty</th>
                        <th>Hold State</th>
                        <th>Stay Time(H)</th>
                        <th>LASTEVENTNAME</th>
                        <th>LASTEVENTTIME</th>
                        <th>LASTEVENTUSER</th>
                        <th>LASTEVENTCOMMENT</th>
                    </tr>
                </thead>
                <tbody>
    """

    # 添加数据行
    for _, row in filtered_df.iterrows():
        # 计算颜色
        product_id_color = product_id_color_map[row['Product ID']]
        lot_grade_color = lot_grade_color_map[row['Lot Grade']]
        hold_state_color = hold_state_color_map[row['Hold State']]

        # Qty颜色渐变（最大值固定为26）
        qty_normalized = row['Qty'] / 26.0 if row['Qty'] <= 26 else 1.0
        qty_lightness = 90 - (qty_normalized * 50)
        qty_color = "hsl(200, 50%, {}%)".format(qty_lightness)

        # Stay Time颜色渐变
        if stay_time_range == 0:
            stay_time_lightness = 70
        else:
            stay_time_normalized = (row['Stay Time(H)'] - min_stay_time) / stay_time_range
            stay_time_lightness = 90 - (stay_time_normalized * 50)
        stay_time_color = "hsl(120, 50%, {}%)".format(stay_time_lightness)

        # 格式化时间
        last_event_time = row['LASTEVENTTIME'].strftime("%Y-%m-%d %H:%M") if pd.notna(row['LASTEVENTTIME']) else ""

        row_html = """
            <tr>
                <td style="background-color: {product_id_color}">{product_id}</td>
                <td>{cst_id}</td>
                <td style="background-color: {lot_grade_color}">{lot_grade}</td>
                <td style="background-color: {qty_color}">{qty}</td>
                <td style="background-color: {hold_state_color}">{hold_state}</td>
                <td style="background-color: {stay_time_color}">{stay_time:.1f}</td>
                <td>{last_event_name}</td>
                <td>{last_event_time}</td>
                <td>{last_event_user}</td>
                <td class="comment-cell" title="{last_event_comment}">{last_event_comment}</td>
            </tr>
        """.format(
            product_id_color=product_id_color,
            product_id=row['Product ID'],
            cst_id=row['CST ID'],
            lot_grade_color=lot_grade_color,
            lot_grade=row['Lot Grade'],
            qty_color=qty_color,
            qty=row['Qty'],
            hold_state_color=hold_state_color,
            hold_state=row['Hold State'],
            stay_time_color=stay_time_color,
            stay_time=row['Stay Time(H)'],
            last_event_name=row['LASTEVENTNAME'],
            last_event_time=last_event_time,
            last_event_user=row['LASTEVENTUSER'],
            last_event_comment=row['LASTEVENTCOMMENT']
        )

        html_content += row_html

    html_content += """
                </tbody>
            </table>
            <div class="notes-container">
                <p class="note">说明：</p>
                <p class="note">① 仅包含IDLE卡夹</p>
                <p class="note">② P等卡夹过滤条件：gls数量≤3。避免Highlight待重投玻璃</p>
            </div>
            <p style="color: #666; font-size: 12px;">本邮件为自动发送，无需回复</p>
        </body>
    </html>
    """

    return html_content

# 按CARRIERNAME分组处理
carrier_summary = []

for carrier_name, group in wip_list.groupby('CARRIERNAME'):
    # ①计算carrier等级
    grades = group['PRODUCTGRADE'].dropna().unique()
    carrier_grade = get_highest_priority(grades, grade_priority)

    # ②计算hold状态
    hold_states = group['PRODUCTHOLDSTATE'].dropna().unique()
    carrier_hold_state = get_highest_priority(hold_states, hold_state_priority)

    # ③计算carrier用量（行数）
    carrier_count = len(group)

    # ④计算carrier最新事件
    latest_event = group.loc[group['LASTEVENTTIME'].idxmax()]

    # ⑤计算IDLE时间
    latest_idle_time = group['LASTIDLETIME'].max()
    current_time = datetime.now()
    idle_hours = round((current_time - pd.to_datetime(latest_idle_time)).total_seconds() / 3600, 1)

    carrier_summary.append({
        'Product ID': group['PRODUCTSPECNAME'].iloc[0],  # 源数据同一carrier只有一个PRODUCTSPECNAME
        'CST ID': carrier_name,
        'Lot Grade': carrier_grade,
        'Hold State': carrier_hold_state,
        'Qty': carrier_count,
        'Stay Time(H)': idle_hours,
        'LASTEVENTNAME': latest_event['LASTEVENTNAME'],
        'LASTEVENTTIME': latest_event['LASTEVENTTIME'],
        'LASTEVENTUSER': latest_event['LASTEVENTUSER'],
        'LASTEVENTCOMMENT': latest_event['LASTEVENTCOMMENT'],
    })

# 转换为DataFrame
carrier_df = pd.DataFrame(carrier_summary)

# 按Product ID、CST ID排序
carrier_df.sort_values(by=['Product ID', 'CST ID'], inplace=True)

# 显式定义列类型
carrier_df = carrier_df.astype({
    'Product ID': 'string',
    'CST ID': 'string',
    'Lot Grade': 'string',
    'Hold State': 'string',
    'Qty': 'int64',
    'Stay Time(H)': 'float64',
    'LASTEVENTNAME': 'string',
    'LASTEVENTTIME': 'datetime64[ns]',
    'LASTEVENTUSER': 'string',
    'LASTEVENTCOMMENT': 'string'
})

time_trigger = datetime.now()

# 生成HTML内容
html_content = generate_html_content(carrier_df)

if platform == 'ide':
    carrier_df.to_excel('carrier_summary.xlsx', index=False)
    if html_content:
        with open('email_content.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("HTML content saved to email_content.html")
    else:
        print("No records match the filter criteria")