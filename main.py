# CPython 3.11

import pandas as pd
from datetime import datetime

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'
    wip_list = pd.read_excel('WIP.xlsx')

# 预定义字段
grade_priority = ['P', 'S', 'T', 'I', 'G', 'R',]
hold_state_priority = ['OnHold', 'NotOnHold',]

def get_highest_priority(values, priority_list):
    """根据优先级列表获取最高优先级的值"""
    for priority in priority_list:
        if priority in values:
            return priority
    return None

# 定义列的显示顺序
COLUMN_ORDER = [
    'Product ID', 'CST ID', 'Lot Grade', 'Qty', 'Hold State', 'Stay Time(H)',
    'LASTEVENTNAME', 'LASTEVENTTIME', 'LASTEVENTUSER', 'LASTEVENTCOMMENT'
]

# 列配置字典：定义每列的显示名称、格式化函数和样式
COLUMN_CONFIG = {
    'Product ID': {
        'display_name': 'Product ID',
        'color_type': 'categorical',
        'colors': ['#FFE6E6', '#E6F3FF', '#E6FFE6', '#FFF0E6', '#F0E6FF', '#E6FFFF'],
        'format_func': lambda x: str(x),
        'cell_class': ''
    },
    'CST ID': {
        'display_name': 'CST ID',
        'color_type': 'none',
        'format_func': lambda x: str(x),
        'cell_class': ''
    },
    'Lot Grade': {
        'display_name': 'Lot Grade',
        'color_type': 'categorical',
        'colors': ['#FFB6C1', '#98FB98', '#87CEFA', '#DDA0DD', '#F0E68C', '#E6E6FA'],
        'format_func': lambda x: str(x),
        'cell_class': ''
    },
    'Qty': {
        'display_name': 'Qty',
        'color_type': 'gradient',
        'gradient_config': {'max_value': 26, 'hue': 200, 'saturation': 50},
        'format_func': lambda x: str(int(x)),
        'cell_class': ''
    },
    'Hold State': {
        'display_name': 'Hold State',
        'color_type': 'categorical',
        'colors': ['#FFA07A', '#90EE90', '#ADD8E6', '#D8BFD8', '#FAFAD2', '#B0C4DE'],
        'format_func': lambda x: str(x),
        'cell_class': ''
    },
    'Stay Time(H)': {
        'display_name': 'Stay Time(H)',
        'color_type': 'range_gradient',
        'gradient_config': {'hue': 120, 'saturation': 50},
        'format_func': lambda x: "{:.1f}".format(float(x)),
        'cell_class': ''
    },
    'LASTEVENTNAME': {
        'display_name': 'LASTEVENTNAME',
        'color_type': 'none',
        'format_func': lambda x: str(x),
        'cell_class': ''
    },
    'LASTEVENTTIME': {
        'display_name': 'LASTEVENTTIME',
        'color_type': 'none',
        'format_func': lambda x: x.strftime("%Y-%m-%d %H:%M") if pd.notna(x) else "",
        'cell_class': ''
    },
    'LASTEVENTUSER': {
        'display_name': 'LASTEVENTUSER',
        'color_type': 'none',
        'format_func': lambda x: str(x),
        'cell_class': ''
    },
    'LASTEVENTCOMMENT': {
        'display_name': 'LASTEVENTCOMMENT',
        'color_type': 'none',
        'format_func': lambda x: str(x),
        'cell_class': 'comment-cell',
        'title_attr': True  # 需要title属性
    }
}


def generate_color_maps(filtered_df):
    """生成颜色映射字典"""
    color_maps = {}

    for col_name, config in COLUMN_CONFIG.items():
        if config['color_type'] == 'categorical':
            unique_values = sorted(filtered_df[col_name].unique())
            colors = config['colors']
            color_maps[col_name] = {val: colors[i % len(colors)]
                                   for i, val in enumerate(unique_values)}
        elif config['color_type'] == 'range_gradient':
            # 计算范围渐变的最小最大值
            min_val = filtered_df[col_name].min()
            max_val = filtered_df[col_name].max()
            color_maps[col_name] = {'min': min_val, 'max': max_val, 'range': max_val - min_val}

    return color_maps


def get_cell_style(col_name, value, color_maps):
    """获取单元格样式"""
    config = COLUMN_CONFIG[col_name]

    if config['color_type'] == 'categorical':
        color = color_maps[col_name].get(value, '#FFFFFF')
        return 'background-color: {}'.format(color)
    elif config['color_type'] == 'gradient':
        # 固定最大值的渐变
        max_val = config['gradient_config']['max_value']
        normalized = float(value) / max_val if float(value) <= max_val else 1.0
        lightness = 90 - (normalized * 50)
        hue = config['gradient_config']['hue']
        saturation = config['gradient_config']['saturation']
        return 'background-color: hsl({}, {}%, {}%)'.format(hue, saturation, lightness)
    elif config['color_type'] == 'range_gradient':
        # 基于数据范围的渐变
        range_info = color_maps[col_name]
        if range_info['range'] == 0:
            lightness = 70
        else:
            normalized = (float(value) - range_info['min']) / range_info['range']
            lightness = 90 - (normalized * 50)
        hue = config['gradient_config']['hue']
        saturation = config['gradient_config']['saturation']
        return 'background-color: hsl({}, {}%, {}%)'.format(hue, saturation, lightness)
    else:
        return ''


def generate_html_content(carrier_df):
    """生成HTML邮件内容"""
    # 过滤条件：(Lot Grade列为R) 或 (Lot Grade为P 且 Qty≤3)
    filtered_df = carrier_df[
        (carrier_df['Lot Grade'] == 'R') |
        ((carrier_df['Lot Grade'] == 'P') & (carrier_df['Qty'] <= 3))
    ]

    if len(filtered_df) == 0:
        return None

    # 生成颜色映射
    color_maps = generate_color_maps(filtered_df)

    # 创建HTML内容
    html_content = """
    <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; }
                table {
                    border-collapse: collapse;
                    width: 100%;
                    margin-top: 20px;
                }
                th {
                    background-color: #4A90E2;
                    color: white;
                    padding: 6px;
                    text-align: center;
                    border: 1px solid #ddd;
                }
                td {
                    padding: 6px;
                    border: 1px solid #ddd;
                    text-align: center;
                    max-width: 120px;
                    word-wrap: break-word;
                }
                .comment-cell {
                    max-width: 150px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    cursor: help;
                }
                .note {
                    color: #666;
                    font-size: 13px;
                    margin: 5px 0;
                    line-height: 1.4;
                }
                .notes-container {
                    background-color: #f9f9f9;
                    padding: 10px;
                    border-left: 3px solid #4A90E2;
                    margin: 10px 0;
                }
            </style>
        </head>
        <body>
            <h3>P等/R等卡夹信息如下，请妥善处理：</h3>
            <table>
                <thead>
                    <tr>
    """

    # 动态生成表头
    for col_name in COLUMN_ORDER:
        if col_name in COLUMN_CONFIG:
            display_name = COLUMN_CONFIG[col_name]['display_name']
            html_content += "                        <th>{}</th>\n".format(display_name)

    html_content += """                    </tr>
                </thead>
                <tbody>
    """

    # 添加数据行
    for _, row in filtered_df.iterrows():
        html_content += "                <tr>\n"

        # 动态生成每个单元格
        for col_name in COLUMN_ORDER:
            if col_name in COLUMN_CONFIG:
                config = COLUMN_CONFIG[col_name]
                value = row[col_name]

                # 格式化值
                formatted_value = config['format_func'](value)

                # 获取样式
                style = get_cell_style(col_name, value, color_maps)
                style_attr = ' style="{}"'.format(style) if style else ''

                # 获取CSS类
                class_attr = ' class="{}"'.format(config['cell_class']) if config['cell_class'] else ''

                # 获取title属性（用于LASTEVENTCOMMENT）
                title_attr = ''
                if config.get('title_attr', False):
                    title_attr = ' title="{}"'.format(formatted_value)

                html_content += '                    <td{}{}{}>{}</td>\n'.format(
                    style_attr, class_attr, title_attr, formatted_value
                )

        html_content += "                </tr>\n"

    html_content += """
                </tbody>
            </table>
            <div class="notes-container">
                <p class="note">说明：</p>
                <p class="note">① 仅包含IDLE卡夹</p>
                <p class="note">② P等卡夹过滤条件：gls数量≤3。避免Highlight待重投玻璃</p>
            </div>
            <p style="color: #666; font-size: 12px;">本邮件为自动发送，无需回复</p>
        </body>
    </html>
    """

    return html_content

# 按CARRIERNAME分组处理
carrier_summary = []

for carrier_name, group in wip_list.groupby('CARRIERNAME'):
    # ①计算carrier等级
    grades = group['PRODUCTGRADE'].dropna().unique()
    carrier_grade = get_highest_priority(grades, grade_priority)

    # ②计算hold状态
    hold_states = group['PRODUCTHOLDSTATE'].dropna().unique()
    carrier_hold_state = get_highest_priority(hold_states, hold_state_priority)

    # ③计算carrier用量（行数）
    carrier_count = len(group)

    # ④计算carrier最新事件
    latest_event = group.loc[group['LASTEVENTTIME'].idxmax()]

    # ⑤计算IDLE时间
    latest_idle_time = group['LASTIDLETIME'].max()
    current_time = datetime.now()
    idle_hours = round((current_time - pd.to_datetime(latest_idle_time)).total_seconds() / 3600, 1)

    carrier_summary.append({
        'Product ID': group['PRODUCTSPECNAME'].iloc[0],  # 源数据同一carrier只有一个PRODUCTSPECNAME
        'CST ID': carrier_name,
        'Lot Grade': carrier_grade,
        'Hold State': carrier_hold_state,
        'Qty': carrier_count,
        'Stay Time(H)': idle_hours,
        'LASTEVENTNAME': latest_event['LASTEVENTNAME'],
        'LASTEVENTTIME': latest_event['LASTEVENTTIME'],
        'LASTEVENTUSER': latest_event['LASTEVENTUSER'],
        'LASTEVENTCOMMENT': latest_event['LASTEVENTCOMMENT'],
    })

# 转换为DataFrame
carrier_df = pd.DataFrame(carrier_summary)

# 按Product ID、CST ID排序
carrier_df.sort_values(by=['Product ID', 'CST ID'], inplace=True)

# 显式定义列类型
carrier_df = carrier_df.astype({
    'Product ID': 'string',
    'CST ID': 'string',
    'Lot Grade': 'string',
    'Hold State': 'string',
    'Qty': 'int64',
    'Stay Time(H)': 'float64',
    'LASTEVENTNAME': 'string',
    'LASTEVENTTIME': 'datetime64[ns]',
    'LASTEVENTUSER': 'string',
    'LASTEVENTCOMMENT': 'string'
})

time_trigger = datetime.now()

# 生成HTML内容
html_content = generate_html_content(carrier_df)

if platform == 'ide':
    carrier_df.to_excel('carrier_summary.xlsx', index=False)
    if html_content:
        with open('email_content.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("HTML content saved to email_content.html")
    else:
        print("No records match the filter criteria")