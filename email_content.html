
    <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; }
                table {
                    border-collapse: collapse;
                    width: 100%;
                    margin-top: 20px;
                }
                th {
                    background-color: #4A90E2;
                    color: white;
                    padding: 6px;
                    text-align: center;
                    border: 1px solid #ddd;
                }
                td {
                    padding: 6px;
                    border: 1px solid #ddd;
                    text-align: center;
                    max-width: 120px;
                    word-wrap: break-word;
                }
                .comment-cell {
                    max-width: 150px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    cursor: help;
                }
                .note {
                    color: #666;
                    font-size: 13px;
                    margin: 5px 0;
                    line-height: 1.4;
                }
                .notes-container {
                    background-color: #f9f9f9;
                    padding: 10px;
                    border-left: 3px solid #4A90E2;
                    margin: 10px 0;
                }
            </style>
        </head>
        <body>
            <h3>P等/R等卡夹信息如下，请妥善处理：</h3>
            <table>
                <thead>
                    <tr>
                        <th>Product ID</th>
                        <th>CST ID</th>
                        <th>Lot Grade</th>
                        <th>Qty</th>
                        <th>Hold State</th>
                        <th>Stay Time(H)</th>
                        <th>LASTEVENTNAME</th>
                        <th>LASTEVENTTIME</th>
                        <th>LASTEVENTUSER</th>
                        <th>LASTEVENTCOMMENT</th>
                    </tr>
                </thead>
                <tbody>
    
            <tr>
                <td style="background-color: #FFE6E6">FA690A010NEC00</td>
                <td>FA0391</td>
                <td style="background-color: #FFB6C1">P</td>
                <td style="background-color: hsl(200, 50%, 88.07692307692308%)">1</td>
                <td style="background-color: #90EE90">OnHold</td>
                <td style="background-color: hsl(120, 50%, 84.62616822429904%)">315.3</td>
                <td>ChangeProductSpec</td>
                <td>2025-08-08 07:38</td>
                <td>F1PHT0100</td>
                <td class="comment-cell" title="LotProcessEnd:PEXsvr13">LotProcessEnd:PEXsvr13</td>
            </tr>
        
            <tr>
                <td style="background-color: #FFE6E6">FA690A010NEC00</td>
                <td>FA0893</td>
                <td style="background-color: #FFB6C1">P</td>
                <td style="background-color: hsl(200, 50%, 88.07692307692308%)">1</td>
                <td style="background-color: #90EE90">OnHold</td>
                <td style="background-color: hsl(120, 50%, 86.72897196261687%)">314.4</td>
                <td>ChangeProductSpec</td>
                <td>2025-08-08 08:30</td>
                <td>F1PHT0100</td>
                <td class="comment-cell" title="LotProcessEnd:PEXsvr23">LotProcessEnd:PEXsvr23</td>
            </tr>
        
            <tr>
                <td style="background-color: #E6F3FF">FAW3AB220NEC00</td>
                <td>FA0770</td>
                <td style="background-color: #FFB6C1">P</td>
                <td style="background-color: hsl(200, 50%, 88.07692307692308%)">1</td>
                <td style="background-color: #FFA07A">NotOnHold</td>
                <td style="background-color: hsl(120, 50%, 77.14953271028037%)">318.5</td>
                <td>ClearHoldTime</td>
                <td>2025-08-08 13:58</td>
                <td>N2400871</td>
                <td class="comment-cell" title="ReasonCode:[FH99],Dept:CF_生产管理科,Owner:N2300585-13236574926,Comment:CF:CNXsvr12:CNXsvr12">ReasonCode:[FH99],Dept:CF_生产管理科,Owner:N2300585-13236574926,Comment:CF:CNXsvr12:CNXsvr12</td>
            </tr>
        
            <tr>
                <td style="background-color: #E6FFE6">FBY50B010NEC00</td>
                <td>FA0031</td>
                <td style="background-color: #98FB98">R</td>
                <td style="background-color: hsl(200, 50%, 88.07692307692308%)">1</td>
                <td style="background-color: #FFA07A">NotOnHold</td>
                <td style="background-color: hsl(120, 50%, 40.0%)">334.4</td>
                <td>CancelTrackIn</td>
                <td>2025-08-07 12:30</td>
                <td>F1PHT0300</td>
                <td class="comment-cell" title="LotProcessEnd:PEXsvr13">LotProcessEnd:PEXsvr13</td>
            </tr>
        
            <tr>
                <td style="background-color: #E6FFE6">FBY50B010NEC00</td>
                <td>FA0586</td>
                <td style="background-color: #FFB6C1">P</td>
                <td style="background-color: hsl(200, 50%, 88.07692307692308%)">1</td>
                <td style="background-color: #90EE90">OnHold</td>
                <td style="background-color: hsl(120, 50%, 58.45794392523361%)">326.5</td>
                <td>ChangeProductSpec</td>
                <td>2025-08-07 20:26</td>
                <td>F1PHT0300</td>
                <td class="comment-cell" title="LotProcessEnd:PEXsvr23">LotProcessEnd:PEXsvr23</td>
            </tr>
        
            <tr>
                <td style="background-color: #E6FFE6">FBY50B010NEC00</td>
                <td>FA0622</td>
                <td style="background-color: #98FB98">R</td>
                <td style="background-color: hsl(200, 50%, 82.3076923076923%)">4</td>
                <td style="background-color: #FFA07A">NotOnHold</td>
                <td style="background-color: hsl(120, 50%, 90.0%)">313.0</td>
                <td>TrackOut</td>
                <td>2025-08-08 09:58</td>
                <td>F1INS0700</td>
                <td class="comment-cell" title="LotProcessEnd:PEXsvr12">LotProcessEnd:PEXsvr12</td>
            </tr>
        
                </tbody>
            </table>
            <div class="notes-container">
                <p class="note">说明：</p>
                <p class="note">① 仅包含IDLE卡夹</p>
                <p class="note">② P等卡夹过滤条件：gls数量≤3。避免Highlight待重投玻璃</p>
            </div>
            <p style="color: #666; font-size: 12px;">本邮件为自动发送，无需回复</p>
        </body>
    </html>
    