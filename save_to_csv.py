# ironpython 2.7
# UTF-8 + BOM，使 Excel 自动识别为 UTF-8
import csv, os, tempfile

data = [
    [u"name", u"age", u"note"],
    [u"王小明", 30, u"测试行1"],
    [u"李小红", 28, u"含中文的行"]
]

out = os.path.join(os.environ.get("TEMP") or tempfile.gettempdir(), "spotfire_out_utf8_bom.csv")

with open(out, "wb") as f:
    # 写入 UTF-8 BOM（字节）
    f.write('\xef\xbb\xbf')
    writer = csv.writer(f)
    for row in data:
        r = []
        for v in row:
            if isinstance(v, unicode):
                r.append(v.encode("utf-8"))
            else:
                r.append(str(v))
        writer.writerow(r)

print("Saved UTF-8 (with BOM) CSV to:", out)
